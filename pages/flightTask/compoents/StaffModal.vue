<template>
  <view>
    <view class="form-box" v-show="show">
      <view class="form-box-title">修改人员</view>
      <view class="section-title">飞行部</view>
      <van-row>
        <van-col span="14">
          <FormItem label="机长：" label-width="70px">
            <input
              class="input-box"
              v-model="formNameData.crewRole1"
              placeholder="请选择"
              disabled
              @click="openPicker('机长类型', 'pilotType1', 'crewRole1')"
            />
          </FormItem>
        </van-col>
        <van-col span="10">
          <FormItem label-width="0px">
            <input
              class="input-box"
              v-model="formNameData.captainId"
              placeholder="请选择"
              disabled
              @click="openPicker('机长', 'pilot', 'captainId')"
            />
          </FormItem>
        </van-col>
      </van-row>
      <van-row>
        <van-col span="14">
          <FormItem label="副机长：" label-width="70px">
            <input
              v-model="formNameData.crewRole2"
              placeholder="请选择"
              disabled
              @click="openPicker('副驾驶类型', 'pilotType2', 'crewRole2')"
            />
          </FormItem>
        </van-col>
        <van-col span="10">
          <FormItem label-width="0px">
            <input
              v-model="formNameData.copilotId"
              placeholder="请选择"
              disabled
              @click="openPicker('副机长', 'pilot', 'copilotId')"
            />
          </FormItem>
        </van-col>
      </van-row>

      <view class="section-title">机务部</view>
      <FormItem label="机械师：" label-width="70px">
        <input
          v-model="formNameData.mechanicMasterId"
          placeholder="请选择"
          disabled
          @click="openPicker('机械师', 'maintenance', 'mechanicMasterId')"
        />
      </FormItem>
      <FormItem label="机械员：" label-width="70px">
        <input
          v-model="formNameData.mechanicId"
          placeholder="请选择"
          disabled
          @click="openPicker('机械员', 'maintenance', 'mechanicId')"
        />
      </FormItem>
      <FormItem label="保障员：" label-width="70px">
        <input
          v-model="formNameData.safetyOfficerId"
          placeholder="请选择"
          disabled
          @click="openPicker('保障员', 'maintenance', 'safetyOfficerId')"
        />
      </FormItem>
      <view class="section-title">运控中心</view>
      <FormItem label="安检：" label-width="70px">
        <input
          v-model="formNameData.inspectorId"
          placeholder="请选择"
          disabled
          @click="openPicker('安检', 'operationControl', 'inspectorId')"
        />
      </FormItem>
      <FormItem label="现场组织：" label-width="70px">
        <input
          v-model="formNameData.organizationId"
          placeholder="请选择"
          disabled
          @click="openPicker('现场组织', 'operationControl', 'organizationId')"
        />
      </FormItem>
      <FormItem label="责任运控：" label-width="70px">
        <input
          v-model="formNameData.ocUserId"
          placeholder="请选择"
          disabled
          @click="openPicker('责任运控', 'operationControl', 'ocUserId')"
        />
      </FormItem>
      <FormItem label="运控助理：" label-width="70px">
        <input
          v-model="formNameData.ocAssistantId"
          placeholder="请选择"
          disabled
          @click="openPicker('运控助理', 'operationControl', 'ocAssistantId')"
        />
      </FormItem>
      <FormItem label="随机人数：" label-width="70px">
        <input v-model="formIdData.accompanyNumber" placeholder="请输入" />
      </FormItem>
      <view class="section-title">市场部</view>
      <FormItem label="售票：" label-width="70px">
        <input
          v-model="formNameData.conductorId"
          placeholder="请选择"
          disabled
          @click="openPicker('售票', 'conductor', 'conductorId')"
        />
      </FormItem>
      <FormItem label="行李数：" label-width="70px">
        <input v-model="formIdData.luggageNumber" placeholder="请选择" />
      </FormItem>
      <view class="submit-btn-box">
        <van-button type="info" @click="submitForm">确认修改</van-button>
      </view>
    </view>
    <van-popup
      :show="pickerData.show"
      position="bottom"
      :lock-scroll="true"
      custom-style="max-height: 60%;"
    >
      <view class="popup-header">
        <text class="cancel" @click="closePicker">取消</text>
        <text class="title">{{ pickerData.title }}</text>
        <text class="confirm" @click="confirmPicker">确认</text>
      </view>
      <view class="popup-content">
        <van-radio-group @change="onChange" :value="formCheckedIdData" :max="1">
          <van-cell
            v-for="(item, index) in pickerData.list"
            :key="index"
            :title="item.userName"
            clickable
            @click="onRowChange(item.userId)"
          >
            <van-radio
              slot="right-icon"
              :name="item.userId"
              :value="item.userId"
            />
          </van-cell>
        </van-radio-group>
      </view>
    </van-popup>
  </view>
</template>
<script>
import FormItem from './FormItem.vue'
import {
  listUserByRole,
  queryCrewInfo,
  updateCrewFlightTask,
} from '../../../api/flightTask'
import { SUCCESS_CODE } from '../../../utils/constant'

export default {
  name: 'StaffModal',
  components: { FormItem },
  props: {
    title: {
      type: String,
      default: '修改人员',
    },
    show: {
      type: Boolean,
      default: false,
    },
    taskId: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      // formCheckedNameData: [],
      formCheckedIdData: '', //选择id的数据
      //下拉选择数据
      pickerData: {
        show: false,
        title: '',
        list: [],
        formKey: '',
      },
      formNameData: {}, //表单名称数据展示用
      formIdData: {}, //表单id数据
      staffObj: {},
    }
  },
  created() {
    this.getPickerListData()
  },
  mounted() {
    this.getPickerListData().then(() => {
      this.getData()
    })
  },
  methods: {
    async getData() {
      const { response: res } = await queryCrewInfo({
        flightTaskConfigId: Number(this.taskId),
      })
      if (res.code === SUCCESS_CODE && res.data) {
        const data = res.data
        this.formNameData = {
          ...data,
          captainId: this.transformPickerData(data.captainId, 'pilot'),
          conductorId: this.transformPickerData(data.conductorId, 'conductor'),
          copilotId: this.transformPickerData(data.copilotId, 'pilot'),
          inspectorId: this.transformPickerData(
            data.inspectorId,
            'operationControl'
          ),
          mechanicId: this.transformPickerData(data.mechanicId, 'maintenance'),
          mechanicMasterId: this.transformPickerData(
            data.mechanicMasterId,
            'maintenance'
          ),
          ocAssistantId: this.transformPickerData(
            data.ocAssistantId,
            'operationControl'
          ),
          ocUserId: this.transformPickerData(data.ocUserId, 'operationControl'),
          organizationId: this.transformPickerData(
            data.organizationId,
            'operationControl'
          ),
          safetyOfficerId: this.transformPickerData(
            data.safetyOfficerId,
            'maintenance'
          ),
        }
        this.formIdData = data || {}
      }
    },
    //获取下拉人员数据
    async getPickerListData() {
      const { response: res } = await listUserByRole()
      if (res.code === SUCCESS_CODE) {
        this.staffObj = {
          ...res.data,
          pilotType1: [
            { userName: '机长', userId: '机长' },
            { userName: '实习机长', userId: '实习机长' },
            { userName: '教员', userId: '教员' },
          ],
          pilotType2: [
            { userName: '副驾驶', userId: '副驾驶' },
            { userName: '副驾驶A', userId: '副驾驶A' },
            { userName: '学员', userId: '学员' },
            { userName: '同乘', userId: '同乘' },
          ],
        }
      }
    },

    openPicker(title, listKey, formKey) {
      this.pickerData = {
        show: true,
        title: title,
        list: this.staffObj[listKey],
        formKey: formKey,
      }
    },
    closePicker() {
      this.pickerData = {
        show: false,
        title: '',
        list: [],
        formKey: '',
      }
      this.formCheckedIdData = ''
    },
    transformPickerData(id, key) {
      if (id && key) {
        const name = this.staffObj[key].find(
          (item) => item.userId == id
        ).userName
        return name || ''
      }
    },
    confirmPicker() {
      if (this.formCheckedIdData) {
        this.formIdData[this.pickerData.formKey] = this.formCheckedIdData
        this.formNameData[this.pickerData.formKey] =
          this.pickerData.list.find(
            (item) => item.userId == this.formCheckedIdData
          ).userName || ''
      }
      this.closePicker()
    },
    onChange(event) {
      this.formCheckedIdData = event.detail
    },
    onRowChange(data) {
      this.formCheckedIdData = data
    },
    async submitForm() {
      const { response: res } = await updateCrewFlightTask({
        ...this.formIdData,
        flightTaskConfigId: Number(this.taskId),
      })
      if (res.code === SUCCESS_CODE) {
        uni.showToast({
          title: res.msg || '操作成功',
          icon: 'success',
        })
        setTimeout(() => {
          this.getData()
        }, 1000)
      }
    },
  },
}
</script>

<style scoped lang="scss">
.form-box {
  padding: 16px;
  background: #fff;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.form-box-title {
  font-weight: bold;
  font-size: 16px;
  text-align: center;
  margin-bottom: 16px;
}

.section-title {
  font-weight: bold;
  font-size: 16px;
  padding: 12px 0 0 0;
  position: relative;

  &::before {
    content: '';
    display: block;
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background: #333;
    position: absolute;
    left: -10px;
    top: calc(50% + 2px);
    //transform: translateY(-50%);
  }
}

.popup-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 16px;
  box-sizing: border-box;

  .cancel {
    color: #999;
    font-size: 12px;
  }

  .confirm {
    color: #2c5de5;
    font-size: 12px;
  }

  .title {
    font-size: 14px;
    font-weight: bold;
    color: #333;
  }
}

.submit-btn-box {
  width: 100%;
  margin: 16px auto;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
}

.flex-row {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 6px;
}
</style>
<style lang="scss">
.popup-content {
  padding: 16px;
  box-sizing: border-box;
  max-height: 80%;
  overflow-y: auto;
}
</style>
