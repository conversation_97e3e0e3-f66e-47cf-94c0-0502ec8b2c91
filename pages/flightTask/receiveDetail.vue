<template>
  <view class="receive-detail-page">
    <!-- 自定义导航栏 -->
    <CustomerNav title="市场任务接收" />

    <!-- 内容主体 -->
    <view class="content">
      <!-- 表单区域 -->
      <view class="form-box">
        <van-row>
          <van-col span="12">
            <FormItem label="注册号：" class="text-row" label-width="60px">
              <text>{{ formData.registrationNumber }}</text>
            </FormItem>
          </van-col>
          <van-col span="12">
            <FormItem label="机型：" class="text-row" label-width="50px">
              <text>{{ formData.aircraftType }}</text>
            </FormItem>
          </van-col>
        </van-row>

        <FormItem label="任务性质：" class="text-row">
          <text>{{ formData.taskType }}</text>
        </FormItem>

        <FormItem label="计划时间：" class="text-row">
          <text
            >{{ formData.flightDate || '' }}({{
              getRelativeDateText(formData.flightDate)
            }})
          </text>
        </FormItem>
        <view
          v-if="formData.taskType && formData.taskType.indexOf('空中游览') > -1"
        >
          <FormItem label="产品名称：" class="text-row">
            <text>{{ formData.productName }}</text>
          </FormItem>
          <FormItem label="套餐名称：" class="text-row">
            <text>{{ formData.packageName }}</text>
          </FormItem>
        </view>
        <view>
          <FormItem
            label="起飞基地："
            class="text-row"
            v-if="!!formData.departure"
          >
            <text>{{ formData.departure }}</text>
          </FormItem>
          <FormItem
            label="降落基地："
            class="text-row"
            v-if="!!formData.arrive"
          >
            <text>{{ formData.arrive }}</text>
          </FormItem>
          <FormItem
            label="经停点："
            class="text-row"
            v-show="!!formData.alternate"
          >
            <text>{{ formData.alternate }}</text>
          </FormItem>
        </view>

        <FormItem label="预估架次：" class="text-row">
          <text>{{ formData.flightFrequency }}</text>
        </FormItem>

        <van-row
          class="text-row"
          v-for="(item, index) in formData.takeOffAndLanding"
          :key="index"
        >
          <van-col span="12">
            <FormItem label="计划起飞：" class="text-row" label-width="70px">
              <text>{{ item.planDepartTime }}</text>
            </FormItem>
          </van-col>
          <van-col>
            <FormItem label="计划到达：" class="text-row" label-width="70px">
              <text>{{ item.planArriveTime }}</text>
            </FormItem>
          </van-col>
        </van-row>

        <FormItem label="高度：" class="text-row">
          <text>{{ formData.altitude }}</text>
        </FormItem>
        <FormItem label="备降场：" class="text-row">
          <text>{{ formData.emergencyLanding }}</text>
        </FormItem>
        <FormItem label="飞行规则：" class="text-row">
          <text>{{ formData.flightRule }}</text>
        </FormItem>
        <FormItem label="机长气象标准：" class="text-row">
          <text>{{ formData.meteorologyStandard }}</text>
        </FormItem>
        <FormItem label="应答机：" class="text-row">
          <text>{{ formData.transponderCode }}</text>
        </FormItem>
        <FormItem label="燃油续航：" class="text-row">
          <text>{{ formData.fuelEndurance }}</text>
        </FormItem>
        <FormItem label="巡航速度：" class="text-row">
          <text>{{ formData.circuitSpeed }}</text>
        </FormItem>

        <FormItem label="航线/空域：" class="text-row">
          <text>{{ formData.routeOrAirspaceName }}</text>
        </FormItem>
        <FormItem label="市场备注：" class="text-row">
          <text>{{ formData.remark }}</text>
        </FormItem>
        <FormItem label="运控备注：" class="text-row">
          <text>{{ formData.ocRemark }}</text>
        </FormItem>
      </view>
      <!-- 提交按钮 -->
      <view class="submit-btn-box">
        <van-button type="info" @click="submitForm">确认接收</van-button>
        <van-button type="info" plain @click="staffModalShow = !staffModalShow"
          >修改人员
        </van-button>
      </view>
      <Background />
      <!-- 人员选择弹窗 -->
      <StaffModal :show="staffModalShow" :task-id="taskId" />
    </view>
  </view>
</template>

<script>
import CustomerNav from '../../components/CutomerNav/index.vue'
import FormItem from './compoents/FormItem.vue'
import StaffModal from './compoents/StaffModal.vue'
import {
  queryFlightTaskConfigDetail,
  receiveConfirmFlightTask,
} from '../../api/flightTask'
import { SUCCESS_CODE } from '../../utils/constant'
import { getRelativeDateText } from '../../utils'
import Background from '../../components/Background/index.vue'

export default {
  name: 'sureDetail',
  components: { Background, StaffModal, CustomerNav, FormItem },
  data() {
    return {
      taskId: '',
      formData: {},
      staffModalShow: false, // 人员选择弹窗
    }
  },
  onLoad: function (option) {
    this.taskId = option.id
  },
  mounted() {
    this.getData()
  },
  methods: {
    getRelativeDateText,
    async getData() {
      const res = await queryFlightTaskConfigDetail({
        flightTaskConfigId: Number(this.taskId),
      })
      if (res.response.code === 200) {
        const data = res.response.data
        this.formData = data || {}
      }
    },

    // 提交表单
    async submitForm() {
      const { response } = await receiveConfirmFlightTask({
        flightTaskConfigId: Number(this.taskId),
      })
      if (response.code === SUCCESS_CODE) {
        uni.showToast({
          title: response.msg || '操作成功',
          icon: 'success',
        })
        setTimeout(() => {
          // uni.navigateBack()
          uni.navigateTo({
            url: '/pages/flightTask/sure?status=2',
          })
        }, 1500)
      }
    },
  },
}
</script>

<style lang="scss" scoped>
@import '../../assets/css/common.less';

.receive-detail-page {
  min-height: 100vh;
}

.content {
  padding: 16px;
}

/* 表单样式 */
.form-box {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

  /deep/ .text-row {
    .value-box {
      border-bottom: none;
      padding: 0;
    }

    .label-box {
      padding: 0;
    }

    .input-box {
      border-bottom: 1px solid #ccc;
    }
  }

  .flex-row {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 6px;
  }
}

.submit-btn-box {
  width: 100%;
  margin: 16px auto;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
}
</style>
